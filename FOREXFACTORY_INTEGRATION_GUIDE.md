# ForexFactory Integration Guide

## Current Status
✅ **Demo Mode**: The economic calendar is working with realistic demo data  
⏳ **ForexFactory API**: Ready to integrate once headers are configured  

## Why ForexFactory is Blocked
ForexFactory uses Cloudflare bot protection that blocks automated requests. However, since you can access it with Postman, we can replicate those exact headers.

## How to Get Working Headers from Postman

### Step 1: Make a Successful Request in Postman
1. Open Postman
2. Create a POST request to: `https://www.forexfactory.com/calendar/apply-settings/1?navigation=0`
3. Set headers:
   ```
   Content-Type: application/json
   ```
4. Set body (raw JSON):
   ```json
   {
     "default_view": "today",
     "impacts": [3, 2],
     "event_types": [1, 2, 3, 4, 5, 7, 8, 9, 10, 11],
     "currencies": [9],
     "begin_date": "2025-06-18",
     "end_date": "2025-06-18"
   }
   ```
5. Send the request and verify it works

### Step 2: Get Exact Request from Postman Console
**Method A: Using Postman Console**
1. In Postman, open the **Console** tab (bottom panel)
2. Find your successful request in the console log
3. Click on it to expand the details
4. Copy the **exact request headers** including any cookies

**Method B: Using Code Generation**
1. In Postman, click the **Code** button (</> icon)
2. Select **HTTP** (not Node.js)
3. Copy the raw HTTP request
4. Look for all headers including `Cookie:` lines

### Step 3: Key Headers to Look For
From your successful Postman request, we need:
- `User-Agent` (probably PostmanRuntime/7.32.3)
- `Cookie` (with fflastvisit, fflastactivity, ffsettingshash, etc.)
- `Accept`
- `Content-Type`
- Any other headers Postman includes

### Step 3: Update the API Route
1. Open `src/app/api/forexfactory/route.ts`
2. Replace the headers in the fetch request with the exact headers from Postman
3. Make sure to include all headers, especially:
   - `User-Agent`
   - `Accept`
   - `Cookie` (if any)
   - `Referer`
   - `Origin`

### Step 4: Test the Integration
1. Uncomment the ForexFactory API code in `src/components/WebhookTab4.tsx` (lines 185-220)
2. Comment out the demo data line (line 178)
3. Test the calendar tab in your app

## Example Headers That Might Work
Based on successful Postman requests, try these headers:

```javascript
headers: {
  'Content-Type': 'application/json',
  'User-Agent': 'PostmanRuntime/7.32.3', // Use exact Postman user agent
  'Accept': '*/*',
  'Accept-Encoding': 'gzip, deflate, br',
  'Connection': 'keep-alive',
  // Add any cookies that Postman shows
  'Cookie': 'your_cookies_here',
  // Add any other headers Postman includes
}
```

## Alternative Approach: Use Postman as Proxy
If direct integration fails, you could:
1. Set up Postman to run the request on a schedule
2. Save results to a JSON file
3. Read that file from your app

## Files to Modify
- `src/app/api/forexfactory/route.ts` - Update headers
- `src/components/WebhookTab4.tsx` - Uncomment real API code
- Test with your app's Calendar tab

## Current Demo Data
The demo data shows realistic events based on:
- **USD events**: For XAUUSD, EURUSD, etc.
- **EUR events**: For EURUSD
- **GBP events**: For GBPUSD
- **Time-based**: Events throughout the trading day
- **Impact levels**: High (3), Medium (2), Low (1)

## Response Format Expected
ForexFactory should return:
```json
{
  "days": [
    {
      "events": [
        {
          "id": "event_id",
          "title": "Event Name",
          "country": "United States",
          "time": "08:30",
          "impact": "High",
          "currency": "USD",
          "actual": "",
          "previous": "213K",
          "forecast": "220K"
        }
      ]
    }
  ]
}
```

Once you get the working headers from Postman, the integration should work perfectly!

import { NextRequest, NextResponse } from 'next/server';

const WEBHOOK_URLS = {
  g_instant: process.env.NEXT_PUBLIC_WEBHOOK_URL_GROUP1,
  g_input: process.env.NEXT_PUBLIC_WEBHOOK_URL_GROUP2,
  g_control: process.env.NEXT_PUBLIC_WEBHOOK_URL_GROUP3,
};

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { group, data } = body;

    if (!group || !WEBHOOK_URLS[group as keyof typeof WEBHOOK_URLS]) {
      return NextResponse.json(
        { error: true, message: 'Invalid group specified' },
        { status: 400 }
      );
    }

    const webhookUrl = WEBHOOK_URLS[group as keyof typeof WEBHOOK_URLS];
    
    if (!webhookUrl) {
      return NextResponse.json(
        { error: true, message: 'Webhook URL not configured' },
        { status: 500 }
      );
    }

    console.log(`Proxying webhook to ${group}: ${webhookUrl}`);
    console.log('Data:', JSON.stringify(data, null, 2));

    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'NextJS-Webhook-Proxy/1.0',
      },
      body: JSON.stringify(data),
    });

    const responseText = await response.text();
    let responseData;

    // Log the raw response for debugging
    console.log(`Response status: ${response.status}`);
    console.log(`Response headers:`, Object.fromEntries(response.headers.entries()));
    console.log(`Raw response:`, responseText.substring(0, 500)); // First 500 chars

    try {
      responseData = JSON.parse(responseText);
    } catch {
      responseData = {
        message: responseText,
        isHtml: responseText.trim().startsWith('<!doctype') || responseText.trim().startsWith('<html')
      };
    }

    // If the webhook server returned an error status, mark as unsuccessful
    const success = response.ok && !responseText.includes('Internal Server Error');

    return NextResponse.json({
      success,
      status: response.status,
      statusText: response.statusText,
      data: responseData,
      webhookUrl, // Include for debugging
    });

  } catch (error: any) {
    console.error('Webhook proxy error:', error);
    return NextResponse.json(
      { 
        error: true, 
        message: error.message || 'Internal server error',
        details: error.toString()
      },
      { status: 500 }
    );
  }
}

// Handle OPTIONS for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}

/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    // Disable ESLint during builds
    ignoreDuringBuilds: true,
  },
  typescript: {
    // Disable TypeScript errors during builds
    ignoreBuildErrors: true,
  },
  webpack: (config, { isServer }) => {
    // Discord.js dependencies should only be bundled on the server side
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        'discord.js': false,
        'zlib-sync': false,
        'bufferutil': false,
        'utf-8-validate': false,
      };
    }

    return config;
  },
  serverExternalPackages: ['discord.js'],
};

module.exports = nextConfig;

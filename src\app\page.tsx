

import "./error-style.css";

export default function HomePage() {
  return (
    <html lang="en">
      <head>
        <title>Welcome</title>
        <meta name="description" content="Welcome to our platform" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </head>
      <body style={{
        margin: 0,
        // paddingLeft: '15px',
        // fontFamily: 'Arial, sans-serif',
        // background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        // minHeight: '100vh',
        // display: 'flex',
        // alignItems: 'center',
        // justifyContent: 'center'
      }}>
        <div className="page">
          <div className="main">
            <h1>Server Error</h1>
            <div className="error-code">403</div>
            <h2>Forbidden</h2>
            <p className="lead">You do not have permission to access this document.</p>
            <hr />
            <p>That's what you can do</p>
            <div className="help-actions">
              <a href="javascript:location.reload();">Reload Page</a>
              <a href="javascript:history.back();">Back to Previous Page</a>
              <a href="/">Home Page</a>
            </div>
          </div>
        </div>
        {/* <div style={{
          textAlign: 'center',
          color: 'white',
          padding: '2rem',
          borderRadius: '10px',
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(10px)',
          boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
          border: '1px solid rgba(255, 255, 255, 0.18)'
        }}>
          <h1 style={{
            fontSize: '3rem',
            marginBottom: '1rem',
            textShadow: '2px 2px 4px rgba(0,0,0,0.3)'
          }}>
            🚀 Welcome
          </h1>
          <p style={{
            fontSize: '1.2rem',
            marginBottom: '2rem',
            opacity: 0.9
          }}>
            Your platform is ready to use
          </p>
          <a
            href="/zd"
            style={{
              display: 'inline-block',
              padding: '12px 30px',
              backgroundColor: '#4CAF50',
              color: 'white',
              textDecoration: 'none',
              borderRadius: '25px',
              fontSize: '1.1rem',
              fontWeight: 'bold',
              transition: 'all 0.3s ease',
              boxShadow: '0 4px 15px 0 rgba(76, 175, 80, 0.3)'
            }}
            onMouseOver={(e) => {
              e.currentTarget.style.backgroundColor = '#45a049';
              e.currentTarget.style.transform = 'translateY(-2px)';
            }}
            onMouseOut={(e) => {
              e.currentTarget.style.backgroundColor = '#4CAF50';
              e.currentTarget.style.transform = 'translateY(0)';
            }}
          >
            📊 Access Trading Platform
          </a>
          <div style={{
            marginTop: '2rem',
            fontSize: '0.9rem',
            opacity: 0.7
          }}>
            <p>🔒 Secure • ⚡ Fast • 📱 Responsive</p>
          </div>
        </div> */}
      </body>
    </html>
  );
}
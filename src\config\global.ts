// config/global.ts

// Currency mapping for symbols to Investing.com country IDs
export const SYMBOL_COUNTRY_MAP: { [key: string]: string } = {
  // Major Pairs
  "XAUUSD": "5", // Gold/USD - United States
  "GBPUSD": "5,4", // GBP/USD - United States, United Kingdom
  "EURUSD": "5,17", // EUR/USD - United States, European Union
  "USDJPY": "5,35", // USD/JPY - United States, Japan
  "USDCHF": "5,12", // USD/CHF - United States, Switzerland
  "AUDUSD": "5,25", // AUD/USD - United States, Australia
  "USDCAD": "5,6", // USD/CAD - United States, Canada
  "NZDUSD": "5,43", // NZD/USD - United States, New Zealand

  // Cross Pairs
  "EURGBP": "17,4", // EUR/GBP - European Union, United Kingdom
  "EURJPY": "17,35", // EUR/JPY - European Union, Japan
  "EURCHF": "17,12", // EUR/CHF - European Union, Switzerland
  "EURAUD": "17,25", // EUR/AUD - European Union, Australia
  "EURCAD": "17,6", // EUR/CAD - European Union, Canada
  "EURNZD": "17,43", // EUR/NZD - European Union, New Zealand
  "GBPJPY": "4,35", // GBP/JPY - United Kingdom, Japan
  "GBPCHF": "4,12", // GBP/CHF - United Kingdom, Switzerland
  "GBPAUD": "4,25", // GBP/AUD - United Kingdom, Australia
  "GBPCAD": "4,6", // GBP/CAD - United Kingdom, Canada
  "GBPNZD": "4,43", // GBP/NZD - United Kingdom, New Zealand
  "AUDJPY": "25,35", // AUD/JPY - Australia, Japan
  "AUDCHF": "25,12", // AUD/CHF - Australia, Switzerland
  "AUDCAD": "25,6", // AUD/CAD - Australia, Canada
  "AUDNZD": "25,43", // AUD/NZD - Australia, New Zealand
  "CADJPY": "6,35", // CAD/JPY - Canada, Japan
  "CADCHF": "6,12", // CAD/CHF - Canada, Switzerland
  "CHFJPY": "12,35", // CHF/JPY - Switzerland, Japan
  "NZDJPY": "43,35", // NZD/JPY - New Zealand, Japan
  "NZDCHF": "43,12", // NZD/CHF - New Zealand, Switzerland
  "NZDCAD": "43,6", // NZD/CAD - New Zealand, Canada

  // Exotic Pairs
  "USDSGD": "5,36", // USD/SGD - United States, Singapore
  "USDHKD": "5,39", // USD/HKD - United States, Hong Kong
  "USDNOK": "5,60", // USD/NOK - United States, Norway
  "USDSEK": "5,9", // USD/SEK - United States, Sweden
  "USDDKK": "5,24", // USD/DKK - United States, Denmark
  "USDPLN": "5,53", // USD/PLN - United States, Poland
  "USDCZK": "5,22", // USD/CZK - United States, Czech Republic
  "USDHUF": "5,33", // USD/HUF - United States, Hungary
  "USDTRY": "5,7", // USD/TRY - United States, Turkey
  "USDZAR": "5,17", // USD/ZAR - United States, South Africa
  "USDMXN": "5,34", // USD/MXN - United States, Mexico

  // Commodities
  "XAGUSD": "5", // Silver/USD - United States
  "XPTUSD": "5", // Platinum/USD - United States
  "XPDUSD": "5", // Palladium/USD - United States

  // Oil
  "USOIL": "5", // WTI Crude Oil - United States
  "UKOIL": "5,4", // Brent Crude Oil - United States, United Kingdom

  // Indices (showing major economic regions)
  "US30": "5", // Dow Jones - United States
  "US500": "5", // S&P 500 - United States
  "NAS100": "5", // NASDAQ - United States
  "UK100": "4", // FTSE 100 - United Kingdom
  "GER30": "56", // DAX - Germany
  "FRA40": "8", // CAC 40 - France
  "JPN225": "35", // Nikkei - Japan
  "AUS200": "25", // ASX 200 - Australia
  "HK50": "39", // Hang Seng - Hong Kong

  // Crypto (US regulation focus)
  "BTCUSD": "5", // Bitcoin/USD - United States
  "ETHUSD": "5", // Ethereum/USD - United States
  "LTCUSD": "5", // Litecoin/USD - United States
  "XRPUSD": "5", // Ripple/USD - United States
};

export const SYMBOL_OPTIONS = [
  // Top priority pairs (XU and GU first as requested)
  { label: "XU", value: "XAUUSD", countries: SYMBOL_COUNTRY_MAP["XAUUSD"] },
  { label: "GU", value: "GBPUSD", countries: SYMBOL_COUNTRY_MAP["GBPUSD"] },

  // Major Forex Pairs
  { label: "EU", value: "EURUSD", countries: SYMBOL_COUNTRY_MAP["EURUSD"] },
  { label: "UJ", value: "USDJPY", countries: SYMBOL_COUNTRY_MAP["USDJPY"] },
  { label: "UC", value: "USDCHF", countries: SYMBOL_COUNTRY_MAP["USDCHF"] },
  { label: "AU", value: "AUDUSD", countries: SYMBOL_COUNTRY_MAP["AUDUSD"] },
  { label: "NU", value: "NZDUSD", countries: SYMBOL_COUNTRY_MAP["NZDUSD"] },
  { label: "USDCAD", value: "USDCAD", countries: SYMBOL_COUNTRY_MAP["USDCAD"] },

  // Cross Pairs
  { label: "EG", value: "EURGBP", countries: SYMBOL_COUNTRY_MAP["EURGBP"] },
  { label: "EJ", value: "EURJPY", countries: SYMBOL_COUNTRY_MAP["EURJPY"] },
  { label: "EC", value: "EURCHF", countries: SYMBOL_COUNTRY_MAP["EURCHF"] },
  { label: "EA", value: "EURAUD", countries: SYMBOL_COUNTRY_MAP["EURAUD"] },
  { label: "EURCAD", value: "EURCAD", countries: SYMBOL_COUNTRY_MAP["EURCAD"] },
  { label: "EURNZD", value: "EURNZD", countries: SYMBOL_COUNTRY_MAP["EURNZD"] },
  { label: "GJ", value: "GBPJPY", countries: SYMBOL_COUNTRY_MAP["GBPJPY"] },
  { label: "GC", value: "GBPCHF", countries: SYMBOL_COUNTRY_MAP["GBPCHF"] },
  { label: "GA", value: "GBPAUD", countries: SYMBOL_COUNTRY_MAP["GBPAUD"] },
  { label: "GBPCAD", value: "GBPCAD", countries: SYMBOL_COUNTRY_MAP["GBPCAD"] },
  { label: "GBPNZD", value: "GBPNZD", countries: SYMBOL_COUNTRY_MAP["GBPNZD"] },
  { label: "AJ", value: "AUDJPY", countries: SYMBOL_COUNTRY_MAP["AUDJPY"] },
  { label: "AC", value: "AUDCHF", countries: SYMBOL_COUNTRY_MAP["AUDCHF"] },
  { label: "AUDCAD", value: "AUDCAD", countries: SYMBOL_COUNTRY_MAP["AUDCAD"] },
  { label: "AUDNZD", value: "AUDNZD", countries: SYMBOL_COUNTRY_MAP["AUDNZD"] },
  { label: "CJ", value: "CADJPY", countries: SYMBOL_COUNTRY_MAP["CADJPY"] },
  { label: "CADCHF", value: "CADCHF", countries: SYMBOL_COUNTRY_MAP["CADCHF"] },
  { label: "CHFJPY", value: "CHFJPY", countries: SYMBOL_COUNTRY_MAP["CHFJPY"] },
  { label: "NJ", value: "NZDJPY", countries: SYMBOL_COUNTRY_MAP["NZDJPY"] },
  { label: "NZDCHF", value: "NZDCHF", countries: SYMBOL_COUNTRY_MAP["NZDCHF"] },
  { label: "NZDCAD", value: "NZDCAD", countries: SYMBOL_COUNTRY_MAP["NZDCAD"] },

  // Exotic Pairs
  { label: "USDSGD", value: "USDSGD", countries: SYMBOL_COUNTRY_MAP["USDSGD"] },
  { label: "USDHKD", value: "USDHKD", countries: SYMBOL_COUNTRY_MAP["USDHKD"] },
  { label: "USDNOK", value: "USDNOK", countries: SYMBOL_COUNTRY_MAP["USDNOK"] },
  { label: "USDSEK", value: "USDSEK", countries: SYMBOL_COUNTRY_MAP["USDSEK"] },
  { label: "USDDKK", value: "USDDKK", countries: SYMBOL_COUNTRY_MAP["USDDKK"] },
  { label: "USDPLN", value: "USDPLN", countries: SYMBOL_COUNTRY_MAP["USDPLN"] },
  { label: "USDCZK", value: "USDCZK", countries: SYMBOL_COUNTRY_MAP["USDCZK"] },
  { label: "USDHUF", value: "USDHUF", countries: SYMBOL_COUNTRY_MAP["USDHUF"] },
  { label: "USDTRY", value: "USDTRY", countries: SYMBOL_COUNTRY_MAP["USDTRY"] },
  { label: "USDZAR", value: "USDZAR", countries: SYMBOL_COUNTRY_MAP["USDZAR"] },
  { label: "USDMXN", value: "USDMXN", countries: SYMBOL_COUNTRY_MAP["USDMXN"] },

  // Commodities
  { label: "XAGUSD", value: "XAGUSD", countries: SYMBOL_COUNTRY_MAP["XAGUSD"] },
  { label: "XPTUSD", value: "XPTUSD", countries: SYMBOL_COUNTRY_MAP["XPTUSD"] },
  { label: "XPDUSD", value: "XPDUSD", countries: SYMBOL_COUNTRY_MAP["XPDUSD"] },

  // Oil
  { label: "USOIL", value: "USOIL", countries: SYMBOL_COUNTRY_MAP["USOIL"] },
  { label: "UKOIL", value: "UKOIL", countries: SYMBOL_COUNTRY_MAP["UKOIL"] },

  // Indices
  { label: "US30", value: "US30", countries: SYMBOL_COUNTRY_MAP["US30"] },
  { label: "US500", value: "US500", countries: SYMBOL_COUNTRY_MAP["US500"] },
  { label: "NAS100", value: "NAS100", countries: SYMBOL_COUNTRY_MAP["NAS100"] },
  { label: "UK100", value: "UK100", countries: SYMBOL_COUNTRY_MAP["UK100"] },
  { label: "GER30", value: "GER30", countries: SYMBOL_COUNTRY_MAP["GER30"] },
  { label: "FRA40", value: "FRA40", countries: SYMBOL_COUNTRY_MAP["FRA40"] },
  { label: "JPN225", value: "JPN225", countries: SYMBOL_COUNTRY_MAP["JPN225"] },
  { label: "AUS200", value: "AUS200", countries: SYMBOL_COUNTRY_MAP["AUS200"] },
  { label: "HK50", value: "HK50", countries: SYMBOL_COUNTRY_MAP["HK50"] },

  // Crypto
  { label: "BTCUSD", value: "BTCUSD", countries: SYMBOL_COUNTRY_MAP["BTCUSD"] },
  { label: "ETHUSD", value: "ETHUSD", countries: SYMBOL_COUNTRY_MAP["ETHUSD"] },
  { label: "LTCUSD", value: "LTCUSD", countries: SYMBOL_COUNTRY_MAP["LTCUSD"] },
  { label: "XRPUSD", value: "XRPUSD", countries: SYMBOL_COUNTRY_MAP["XRPUSD"] },
];
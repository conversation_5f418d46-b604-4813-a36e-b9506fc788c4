"use client";
import { useState } from "react";
import { useAppSettings } from "@/contexts/AppSettingsContext";
import WebhookTab1 from "../components/WebhookTab1";
import WebhookTab2 from "../components/WebhookTab2";
import WebhookTab3 from "../components/WebhookTab3";
import WebhookTab4 from "../components/WebhookTab4";
import WebhookTab5 from "../components/WebhookTab5";

export default function Home() {
  const { settings } = useAppSettings();
  const [activeTab, setActiveTab] = useState<"g_instant" | "g_input" | "g_control" | "g_calendar" | "g_discord">("g_control");
  const [loading, setLoading] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);
 
  // Test connectivity function
  const testConnection = async (_url: string, groupName: string) => {
    const group = groupName.toLowerCase().replace(' ', '') as "g_instant" | "g_input" | "g_control" | "g_calendar" | "g_discord";
    setLogs((prev) => [`🔍 Testing connection to ${groupName} via proxy`, ...prev]);

    try {
      const res = await fetch('/api/webhook', {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          group,
          data: { test: true, message: "Connection test" }
        }),
      });

      const result = await res.json();
      if (result.success) {
        setLogs((prev) => [`✅ ${groupName} test successful: ${JSON.stringify(result.data)}`, ...prev]);
      } else {
        setLogs((prev) => [`⚠️ ${groupName} test response: ${JSON.stringify(result)}`, ...prev]);
      }
    } catch (err: any) {
      setLogs((prev) => [`❌ ${groupName} connection test failed: ${err.message}`, ...prev]);
    }
  };

  const sendWebhook = async (
    group: "g_instant" | "g_input" | "g_control" | "g_discord",
    url: string,
    data: {}
  ) => {
    if (!url) {
      alert("❌ Webhook URL ไม่ถูกตั้งค่าใน .env.local");
      return;
    }

    setLoading(true);
    setLogs((prev) => [`⏳ Sending to ${group} with: ${JSON.stringify(data)}`, ...prev]);

    try {
      // Use the proxy API route instead of direct webhook call
      const res = await fetch('/api/webhook', {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Accept": "application/json"
        },
        body: JSON.stringify({ group, data }),
      });

      // Log response status for debugging
      setLogs((prev) => [`📊 Proxy Response Status: ${res.status} ${res.statusText}`, ...prev]);

      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status} ${res.statusText}`);
      }

      const result = await res.json();

      if (result.success) {
        setLogs((prev) => [`✅ Success: ${JSON.stringify(result.data)}`, ...prev]);
      } else {
        setLogs((prev) => [`❌ Webhook Error (${result.status}): ${JSON.stringify(result.data)}`, ...prev]);
      }

    } catch (err: any) {
      // More detailed error logging
      console.error('Webhook error:', err);

      if (err.name === 'TypeError' && err.message.includes('fetch')) {
        setLogs((prev) => [`❌ Network Error: Cannot reach proxy API. Check if your Next.js server is running.`, ...prev]);
      } else if (err.message.includes('HTTP error')) {
        setLogs((prev) => [`❌ HTTP Error: ${err.message}`, ...prev]);
      } else {
        setLogs((prev) => [`❌ Error: ${err.message}`, ...prev]);
      }
    } finally {
      setLoading(false);
    }
  };

  const customStyle = {
    option: (base: any, state: any) => ({
      ...base,
      backgroundColor: state.isSelected
        ? "#2563eb" // สีเมื่อเลือก
        : state.isFocused
          ? "#1e293b" // สีเมื่อ hover
          : "#0f172a", // สีปกติ
      color: "white",
      cursor: "pointer",
    }),
    control: (base: any) => ({
      ...base,
      backgroundColor: "#1f2937",
      color: "white",
      borderColor: "#4b5563",
    }),
    menu: (base: any) => ({
      ...base,
      backgroundColor: "#1f2937",
      color: "white",
    }),
    singleValue: (base: any) => ({ ...base, color: "white" }),
    input: (base: any) => ({ ...base, color: "white" }),
    placeholder: (base: any) => ({ ...base, color: "#d1d5db" }),
  }; 

  return (
    <div className="grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-3 pb-20 gap-16 sm:p-2 font-[family-name:var(--font-geist-sans)]">
      <main className="flex w-full flex-col gap-[12px] row-start-2 items-center sm:items-start">

        <div className="w-full max-w-5xl mx-auto p-4 text-white bg-gray-900 min-h-screen">
          {settings.showTabNavigation && (
            <div className="flex space-x-4 mb-6 border-b border-gray-700">
              {settings.visibleTabs.instant && (
                <button
                  className={`px-4 py-2 text-sm font-medium ${activeTab === "g_instant" ? "border-b-2 border-indigo-400" : "text-gray-400"
                    }`}
                  onClick={() => setActiveTab("g_instant")}
                >
                  Instant
                </button>
              )}
              {settings.visibleTabs.input && (
                <button
                  className={`px-4 py-2 text-sm font-medium ${activeTab === "g_input" ? "border-b-2 border-indigo-400" : "text-gray-400"
                    }`}
                  onClick={() => setActiveTab("g_input")}
                >
                  Input
                </button>
              )}
              {settings.visibleTabs.control && (
                <button
                  className={`px-4 py-2 text-sm font-medium ${activeTab === "g_control" ? "border-b-2 border-indigo-400" : "text-gray-400"
                    }`}
                  onClick={() => setActiveTab("g_control")}
                >
                  Control
                </button>
              )}
              {settings.visibleTabs.calendar && (
                <button
                  className={`px-4 py-2 text-sm font-medium ${activeTab === "g_calendar" ? "border-b-2 border-indigo-400" : "text-gray-400"
                    }`}
                  onClick={() => setActiveTab("g_calendar")}
                >
                  Calendar
                </button>
              )}
              {settings.visibleTabs.discord && (
                <button
                  className={`px-4 py-2 text-sm font-medium ${activeTab === "g_discord" ? "border-b-2 border-indigo-400" : "text-gray-400"
                    }`}
                  onClick={() => setActiveTab("g_discord")}
                >
                  Discord Bot
                </button>
              )}
            </div>
          )}

          {/* Connection Test Buttons */}
          {/* <div className="flex space-x-2 mb-4">
            <button
              onClick={() => testConnection(process.env.NEXT_PUBLIC_WEBHOOK_URL_GROUP1 || '', 'g_instant')}
              className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
              disabled={loading}
            >
              🔍 Test Group 1
            </button>
            <button
              onClick={() => testConnection(process.env.NEXT_PUBLIC_WEBHOOK_URL_GROUP2 || '', 'g_input')}
              className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
              disabled={loading}
            >
              🔍 Test Group 2
            </button>
          </div> */}

          <div className={`grid gap-8 ${activeTab === "g_calendar" || activeTab === "g_discord" ? "grid-cols-1" : "grid-cols-1 lg:grid-cols-2"}`}>
            <div>
              {activeTab === "g_instant" ? (
                <WebhookTab1 customStyle={customStyle} sendWebhook={sendWebhook} loading={loading} />
              ) :  activeTab === "g_input" ? (
                <WebhookTab2 customStyle={customStyle}  sendWebhook={sendWebhook} loading={loading} />
              ) : activeTab === "g_control" ? (
                <WebhookTab3 customStyle={customStyle}  sendWebhook={sendWebhook} loading={loading} />
              ) : activeTab === "g_discord" ? (
                <WebhookTab5 customStyle={customStyle}  sendWebhook={sendWebhook} loading={loading} />
              ) : (
                <WebhookTab4 customStyle={customStyle}  sendWebhook={sendWebhook} loading={loading} />
              )}
            </div>

            {/* Hide Response Logs column when calendar or discord tab is active */}
            {activeTab !== "g_calendar" && activeTab !== "g_discord" && (
              <div>
                <h2 className="text-lg font-semibold mb-2">📜 Response Logs</h2>
                <div className="bg-gray-800 border border-gray-700 rounded-lg p-4 h-80 overflow-y-auto text-sm">
                  {logs.length === 0 ? (
                    <p className="text-gray-500">ไม่มี log</p>
                  ) : (
                    logs.map((log, idx) => (
                      <div key={idx} className="mb-2 text-gray-300">
                        {log}
                      </div>
                    ))
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}

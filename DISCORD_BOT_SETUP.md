# Discord Bot Setup Guide

This guide will help you set up Discord bots to automatically parse trading signals from Discord channels and send them to your webhooks.

## Features

- **Multiple Bot Support**: Add and manage multiple Discord bots
- **Auto Message Parsing**: Uses the same `parseLongText()` function from WebhookTab2
- **Webhook Integration**: Automatically sends parsed data to WEBHOOK_GROUP_2
- **Easy Configuration**: Web-based interface for bot management
- **Permission Management**: Granular control over bot permissions
- **Invite Link Generation**: Automatically generate bot invite links

## Setup Steps

### 1. Create a Discord Application

1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Click "New Application"
3. Give your application a name (e.g., "Trading Signal Bot")
4. Go to the "Bot" section
5. Click "Add Bot"
6. Copy the Bot Token (you'll need this later)
7. Copy the Application ID (Client ID) from the "General Information" section

### 2. Configure <PERSON><PERSON> in the App

1. Open your trading app and go to the "Discord Bot" tab
2. Click "Add Bot"
3. Fill in the required information:
   - **Bot Name**: A friendly name for your bot
   - **Server ID**: The Discord server ID where the bot will listen
   - **Channel ID**: The specific channel ID to monitor for messages
   - **Webhook URL**: Will default to WEBHOOK_GROUP_2, but you can customize it

### 3. Set Bot Permissions

The default permissions are:
- ✅ **View Channel** (required)
- ✅ **Read Message History** (required)
- ⬜ Send Messages
- ⬜ Manage Messages
- ⬜ Embed Links
- ⬜ Attach Files
- ⬜ Use External Emojis
- ⬜ Add Reactions
- ⬜ Mention Everyone
- ⬜ Manage Webhooks
- ⬜ Use Slash Commands

You can customize these based on your needs.

### 4. Generate Invite Link

1. After creating the bot, click "Generate Invite Link"
2. Enter your Discord Application Client ID
3. Copy the generated invite link
4. Open the link in your browser and invite the bot to your server

### 5. Start the Bot

1. Enter your Bot Token in the token field
2. Click "Start" to activate the bot
3. The bot will now monitor the specified channel for trading signals

## How It Works

### Message Parsing

The bot uses the same parsing logic as WebhookTab2's `parseLongText()` function. It looks for:

- **TP (Take Profit)**: `TP1: 2650.50`, `TP2: 2655.00`, etc.
- **SL (Stop Loss)**: `SL: 2640.00`
- **Symbol**: `Symbol: XAUUSD`
- **Signal**: `Signal: Buy Limit`
- **Price Range**: `Price: 2645.00-2650.00`
- **Comment**: `C.SCALP` becomes `SIG_C_SCALP`

### Example Message Format

```
Signal: Buy Limit
Symbol: XAUUSD
Price: 2645.00-2650.00
SL: 2640.00
TP1: 2655.00
TP2: 2660.00
TP3: 2665.00
C.SCALP
```

This will be parsed and sent to your webhook as JSON data.

## Security Notes

- **Bot Tokens**: Store bot tokens securely in your .env file
- **Permissions**: Only grant necessary permissions to your bots
- **Channel Access**: Ensure bots only have access to intended channels
- **Webhook URLs**: Keep webhook URLs private and secure

## Troubleshooting

### Bot Won't Start
- Check that the bot token is correct
- Verify the bot has been invited to the server
- Ensure the server ID and channel ID are correct

### Messages Not Being Processed
- Check that the bot has "View Channel" and "Read Message History" permissions
- Verify the channel ID is correct
- Check the message format matches the expected pattern

### Webhook Errors
- Verify the webhook URL is accessible
- Check that the webhook endpoint accepts JSON data
- Review the webhook logs for error details

## Adding More Bots

To add additional bots:

1. Create new Discord applications following step 1
2. Use the "Add Bot" button in the Discord Bot tab
3. Configure each bot with different server/channel combinations
4. Each bot can have its own webhook URL if needed

## Environment Variables

Add bot tokens to your `.env` file:

```env
# Discord Bot Configuration
DISCORD_BOT_TOKEN_1=your_first_bot_token_here
DISCORD_BOT_TOKEN_2=your_second_bot_token_here
```

Note: The current implementation stores tokens temporarily in the browser for security. For production use, consider storing tokens server-side.
